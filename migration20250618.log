2025-06-18 22:29:17.090 +08:00 [INF] 开始执行数据库迁移命令，配置文件: src/TryCode.DatabaseMigration.CLI/test-partition.config.json
2025-06-18 22:29:47.471 +08:00 [ERR] 未知错误: Could not find color or style 'T'.
System.InvalidOperationException: Could not find color or style 'T'.
   at Spectre.Console.StyleParser.Parse(String text) in /_/src/Spectre.Console/StyleParser.cs:line 10
   at Spectre.Console.MarkupParser.Parse(String text, Style style) in /_/src/Spectre.Console/Internal/Text/Markup/MarkupParser.cs:line 22
   at Spectre.Console.AnsiConsoleExtensions.Markup(IAnsiConsole console, String value) in /_/src/Spectre.Console/Extensions/AnsiConsoleExtensions.Markup.cs:line 77
   at Spectre.Console.AnsiConsoleExtensions.MarkupLine(IAnsiConsole console, String value) in /_/src/Spectre.Console/Extensions/AnsiConsoleExtensions.Markup.cs:line 117
   at Spectre.Console.AnsiConsole.MarkupLine(String value) in /_/src/Spectre.Console/AnsiConsole.Markup.cs:line 81
   at TryCode.DatabaseMigration.CLI.Commands.MigrationCommand.WriteError(String message) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.CLI/Commands/MigrationCommand.cs:line 458
   at TryCode.DatabaseMigration.CLI.Commands.MigrationCommand.DisplayMigrationPlanAsync(MigrationConfig config) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.CLI/Commands/MigrationCommand.cs:line 407
   at TryCode.DatabaseMigration.CLI.Commands.MigrationCommand.ExecuteMigrationCommandAsync() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.CLI/Commands/MigrationCommand.cs:line 158
2025-06-18 22:29:48.075 +08:00 [INF] 正在关闭日志记录器...
