{"MigrationId": "75e0060e-168b-4b50-8ca7-29d5d48c8454", "SourceType": "MySQL", "TargetType": "MySQL", "StartTime": "2025-06-18T22:22:20.784028+08:00", "LastUpdateTime": "2025-06-18T22:24:29.681774+08:00", "Status": "Failed", "TotalTables": 0, "CompletedTables": 0, "TotalRows": 0, "MigratedRows": 0, "TableCheckpoints": {"LedgerData_LedgerAuditedData": {"TableName": "LedgerData_LedgerAuditedData", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-18T22:24:28.494237+08:00", "LastUpdateTime": "2025-06-18T22:24:29.691106+08:00", "Status": "Failed", "ErrorMessage": "Unable to cast object of type 'TryCode.DatabaseMigration.Parallel.CheckpointManagerAdapter' to type 'TryCode.DatabaseMigration.Checkpoint.CheckpointManager'.", "SchemaOnly": false, "IsPartitioned": true, "PartitionCheckpoints": {}}}, "ErrorMessage": "表 LedgerData_LedgerAuditedData 迁移失败: Unable to cast object of type 'TryCode.DatabaseMigration.Parallel.CheckpointManagerAdapter' to type 'TryCode.DatabaseMigration.Checkpoint.CheckpointManager'."}