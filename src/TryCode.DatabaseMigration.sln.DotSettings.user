﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AConfigurationBinder_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FLibrary_003FApplication_0020Support_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F553ea316c6024e8291703d69748c262d8918_003F58_003Fadd76008_003FConfigurationBinder_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIProgress_00601_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FLibrary_003FApplication_0020Support_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F6d5d64e8fdc04759a349f5f29b853e9fb58200_003F78_003F2cc82ed9_003FIProgress_00601_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ASqlMapper_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003FLibrary_003FApplication_0020Support_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F8531eac3f112488f95dcaeb34e39cf7e38400_003F76_003Fc2257ca1_003FSqlMapper_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ASqlMapper_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003F_002E_002E_003F_002E_002E_003FLibrary_003FApplication_0020Support_003FJetBrains_003FRider2024_002E3_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F4120bce0f6814d47b071ef885ad9e8183b600_003Fce_003Fb697efd8_003FSqlMapper_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=08f4823e_002D64b0_002D4b27_002D8256_002D0322655809e5/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="ExecuteAsync_MigratesTablesInParallel" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;
  &lt;Or&gt;
    &lt;TestAncestor&gt;
      &lt;TestId&gt;xUnit::8D376883-8477-4B05-8D09-5E6CF81E79B4::net8.0::TryCode.DatabaseMigration.Tests.Integration.PostgresToMySqlMigrationTests&lt;/TestId&gt;
      &lt;TestId&gt;xUnit::8D376883-8477-4B05-8D09-5E6CF81E79B4::net8.0::TryCode.DatabaseMigration.Tests.Integration.PartitionTableMigrationTests&lt;/TestId&gt;
      &lt;TestId&gt;xUnit::8D376883-8477-4B05-8D09-5E6CF81E79B4::net8.0::TryCode.DatabaseMigration.Tests.Parallel.ParallelMigrationExecutorTests&lt;/TestId&gt;
      &lt;TestId&gt;xUnit::8D376883-8477-4B05-8D09-5E6CF81E79B4::net8.0::TryCode.DatabaseMigration.Tests.MySQL.MySqlDataWriterTests&lt;/TestId&gt;
      &lt;TestId&gt;xUnit::8D376883-8477-4B05-8D09-5E6CF81E79B4::net8.0::TryCode.DatabaseMigration.Tests.Configuration.ConfigurationProviderTests&lt;/TestId&gt;
      &lt;TestId&gt;xUnit::8D376883-8477-4B05-8D09-5E6CF81E79B4::net8.0::TryCode.DatabaseMigration.Tests.MySQL.MySqlDataReaderTests&lt;/TestId&gt;
      &lt;TestId&gt;xUnit::8D376883-8477-4B05-8D09-5E6CF81E79B4::net8.0::TryCode.DatabaseMigration.Tests.MySQL.MySqlCompositePrimaryKeyTests&lt;/TestId&gt;
    &lt;/TestAncestor&gt;
    &lt;ProjectFolder&gt;8D376883-8477-4B05-8D09-5E6CF81E79B4/d:PostgreSQL&lt;/ProjectFolder&gt;
    &lt;ProjectFolder&gt;8D376883-8477-4B05-8D09-5E6CF81E79B4/d:Integration&lt;/ProjectFolder&gt;
    &lt;ProjectFile&gt;8D376883-8477-4B05-8D09-5E6CF81E79B4/d:CLI/f:MigrationOptionsTests.cs&lt;/ProjectFile&gt;
  &lt;/Or&gt;
&lt;/SessionState&gt;</s:String></wpf:ResourceDictionary>