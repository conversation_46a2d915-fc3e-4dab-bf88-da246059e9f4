using System;
using System.Text;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.MySQL.Writers
{
    /// <summary>
    /// MySQL分区管理器
    /// </summary>
    public class MySqlPartitionManager
    {
        private readonly ILogger _logger;
        
        /// <summary>
        /// 初始化MySQL分区管理器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MySqlPartitionManager(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 添加分区定义到SQL
        /// </summary>
        /// <param name="sql">SQL构建器</param>
        /// <param name="schema">表结构信息</param>
        public void AppendPartitionDefinition(StringBuilder sql, TableSchema schema)
        {
            if (!schema.IsPartitioned || string.IsNullOrEmpty(schema.PartitionKey))
            {
                _logger.LogWarning("表 {TableName} 的分区配置无效", schema.Name);
                return;
            }
            
            try
            {
                // 处理不同类型的分区
                if (schema.PartitionType?.Equals("HASH", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendHashPartition(sql, schema);
                }
                else if (schema.PartitionType?.Equals("RANGE", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendRangePartition(sql, schema);
                }
                else if (schema.PartitionType?.Equals("LIST", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendListPartition(sql, schema);
                }
                else if (schema.PartitionType?.Equals("KEY", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendKeyPartition(sql, schema);
                }
                else
                {
                    _logger.LogWarning("不支持的分区类型 {PartitionType}", schema.PartitionType);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成分区定义时出错: {ErrorMessage}", ex.Message);
            }
        }
        
        private void AppendHashPartition(StringBuilder sql, TableSchema schema)
        {
            // 确保分区数量至少为4，这是MySQL中HASH分区的推荐最小值
            var partitionCount = Math.Max(schema.PartitionCount, 4);
            
            // 获取分区键，如果没有指定分区键但有主键，则使用主键作为分区键
            var partitionKey = schema.PartitionKey;
            if (string.IsNullOrEmpty(partitionKey) && schema.PartitionKeys.Count > 0)
            {
                partitionKey = schema.PartitionKeys[0];
                _logger.LogInformation("使用第一个分区键 {PartitionKey} 作为HASH分区键", partitionKey);
            }
            else if (string.IsNullOrEmpty(partitionKey) && schema.PrimaryKeys.Count > 0)
            {
                partitionKey = schema.PrimaryKeys[0];
                _logger.LogInformation("未指定HASH分区键，使用主键 {PrimaryKey} 作为分区键", partitionKey);
            }
            
            if (string.IsNullOrEmpty(partitionKey))
            {
                _logger.LogWarning("表 {TableName} 的HASH分区未指定分区键且没有主键", schema.Name);
                return;
            }
            
            sql.AppendLine($"PARTITION BY HASH (`{partitionKey}`)");
            sql.AppendLine($"PARTITIONS {partitionCount}");
            
            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个HASH分区", schema.Name, partitionCount);
        }
        
        private void AppendKeyPartition(StringBuilder sql, TableSchema schema)
        {
            // 确保分区数量至少为4，这是MySQL中KEY分区的推荐最小值
            var partitionCount = Math.Max(schema.PartitionCount, 4);
            
            // 获取分区键，如果没有指定分区键但有主键，则使用主键作为分区键
            var partitionKey = schema.PartitionKey;
            if (string.IsNullOrEmpty(partitionKey) && schema.PrimaryKeys.Count > 0)
            {
                partitionKey = schema.PrimaryKeys[0];
                _logger.LogInformation("未指定KEY分区键，使用主键 {PrimaryKey} 作为分区键", partitionKey);
            }
            
            if (string.IsNullOrEmpty(partitionKey))
            {
                _logger.LogWarning("表 {TableName} 的KEY分区未指定分区键且没有主键", schema.Name);
                return;
            }
            
            sql.AppendLine($"PARTITION BY KEY (`{partitionKey}`)");
            sql.AppendLine($"PARTITIONS {partitionCount}");
            
            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个KEY分区", schema.Name, partitionCount);
        }
        
        private void AppendRangePartition(StringBuilder sql, TableSchema schema)
        {
            if (schema.PartitionDefinitions == null || schema.PartitionDefinitions.Count == 0)
            {
                _logger.LogWarning("表 {TableName} 的RANGE分区定义为空", schema.Name);
                return;
            }
            
            // 确保分区键存在
            if (string.IsNullOrEmpty(schema.PartitionKey) && schema.PartitionKeys.Count > 0)
            {
                schema.PartitionKey = schema.PartitionKeys[0];
                _logger.LogInformation("使用第一个分区键 {PartitionKey} 作为RANGE分区键", schema.PartitionKey);
            }
            
            if (string.IsNullOrEmpty(schema.PartitionKey))
            {
                _logger.LogWarning("表 {TableName} 的RANGE分区键未指定", schema.Name);
                return;
            }
            
            sql.AppendLine($"PARTITION BY RANGE (`{schema.PartitionKey}`) (");
            
            for (int i = 0; i < schema.PartitionDefinitions.Count; i++)
            {
                var def = schema.PartitionDefinitions[i];
                
                // 从分区条件中提取分区值
                string partitionValue = ExtractRangePartitionValue(def);
                
                // 如果没有从Value属性或Condition属性中提取到值，则跳过此分区
                if (string.IsNullOrEmpty(partitionValue))
                {
                    _logger.LogWarning("无法从分区 {PartitionName} 中提取RANGE分区值，跳过此分区", def.Name);
                    continue;
                }
                
                sql.Append($"    PARTITION p{i} VALUES LESS THAN ({partitionValue})");
                
                if (i < schema.PartitionDefinitions.Count - 1)
                {
                    sql.AppendLine(",");
                }
                else
                {
                    sql.AppendLine();
                }
            }
            
            sql.AppendLine(")");
            
            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个RANGE分区", 
                schema.Name, schema.PartitionDefinitions.Count);
        }
        
        private string ExtractRangePartitionValue(PartitionDefinition def)
        {
            // 首先检查Value属性是否已有值
            if (!string.IsNullOrEmpty(def.Value))
            {
                return def.Value;
            }
            
            // 尝试从Condition属性中提取值
            // PostgreSQL的RANGE分区条件格式为: FOR VALUES FROM (2023) TO (2024)
            if (!string.IsNullOrEmpty(def.Condition))
            {
                var match = Regex.Match(def.Condition, @"TO\s*\((.*?)\)", RegexOptions.IgnoreCase);
                
                if (match.Success && match.Groups.Count > 1)
                {
                    // 将提取的值保存到Value属性中，以便后续使用
                    def.Value = match.Groups[1].Value.Trim();
                    return def.Value;
                }
            }
            
            return string.Empty;
        }
        
        private void AppendListPartition(StringBuilder sql, TableSchema schema)
        {
            if (schema.PartitionDefinitions == null || schema.PartitionDefinitions.Count == 0)
            {
                _logger.LogWarning("表 {TableName} 的LIST分区定义为空", schema.Name);
                return;
            }
            
            // 确保分区键存在
            if (string.IsNullOrEmpty(schema.PartitionKey) && schema.PartitionKeys.Count > 0)
            {
                schema.PartitionKey = schema.PartitionKeys[0];
                _logger.LogInformation("使用第一个分区键 {PartitionKey} 作为LIST分区键", schema.PartitionKey);
            }
            
            if (string.IsNullOrEmpty(schema.PartitionKey))
            {
                _logger.LogWarning("表 {TableName} 的LIST分区键未指定", schema.Name);
                return;
            }
            
            sql.AppendLine($"PARTITION BY LIST (`{schema.PartitionKey}`) (");
            
            for (int i = 0; i < schema.PartitionDefinitions.Count; i++)
            {
                var def = schema.PartitionDefinitions[i];
                
                // 从分区条件中提取分区值
                string partitionValues = ExtractListPartitionValues(def);
                
                // 如果没有从Value属性或Condition属性中提取到值，则跳过此分区
                if (string.IsNullOrEmpty(partitionValues))
                {
                    _logger.LogWarning("无法从分区 {PartitionName} 中提取LIST分区值，跳过此分区", def.Name);
                    continue;
                }
                
                sql.Append($"    PARTITION p{i} VALUES IN ({partitionValues})");
                
                if (i < schema.PartitionDefinitions.Count - 1)
                {
                    sql.AppendLine(",");
                }
                else
                {
                    sql.AppendLine();
                }
            }
            
            sql.AppendLine(")");
            
            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个LIST分区", 
                schema.Name, schema.PartitionDefinitions.Count);
        }
        
        /// <summary>
        /// 从PostgreSQL分区定义中提取LIST分区值
        /// </summary>
        private string ExtractListPartitionValues(PartitionDefinition def)
        {
            // 首先检查Value属性是否已有值
            if (!string.IsNullOrEmpty(def.Value))
            {
                return def.Value;
            }
            
            // 尝试从Condition属性中提取值
            // PostgreSQL的LIST分区条件格式为: FOR VALUES IN (1, 2, 3)
            if (!string.IsNullOrEmpty(def.Condition))
            {
                var match = System.Text.RegularExpressions.Regex.Match(
                    def.Condition, @"IN\s*\((.*?)\)", 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                
                if (match.Success && match.Groups.Count > 1)
                {
                    // 将提取的值保存到Value属性中，以便后续使用
                    def.Value = match.Groups[1].Value.Trim();
                    return def.Value;
                }
            }
            
            return string.Empty;
        }
    }
}
