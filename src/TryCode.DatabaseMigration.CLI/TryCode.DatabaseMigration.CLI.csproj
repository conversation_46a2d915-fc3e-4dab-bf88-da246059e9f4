<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.1" />
    <PackageReference Include="Serilog" Version="2.12.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Spectre.Console" Version="0.46.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TryCode.DatabaseMigration.Configuration\TryCode.DatabaseMigration.Configuration.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Core\TryCode.DatabaseMigration.Core.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.MySQL\TryCode.DatabaseMigration.MySQL.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.PostgreSQL\TryCode.DatabaseMigration.PostgreSQL.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Parallel\TryCode.DatabaseMigration.Parallel.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Checkpoint\TryCode.DatabaseMigration.Checkpoint.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="pg-to-mysql.config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="migrate-postgres-to-mysql.bat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="migrate-postgres-to-mysql.sh">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="pg-to-mysql-ledgerdata.config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
