{"Source": {"Type": "PostgreSQL", "ConnectionString": "Host=localhost;Port=5432;Database=testdb;Username=test;Password=test", "BatchSize": 1000}, "Target": {"Type": "MySQL", "ConnectionString": "Server=localhost;Port=3306;Database=testdb;Uid=test;Pwd=test", "BatchSize": 1000}, "Tables": ["LedgerData_LedgerAuditedData", "LedgerData_LedgerAuditedData_0", "LedgerData_LedgerAuditedData_1", "LedgerData_LedgerAuditedData_2"], "ExcludedTables": [], "EnableCheckpoint": true, "CheckpointFilePath": "test_checkpoint.json", "ParallelDegree": 2, "IgnoreErrors": false, "CreateTargetDatabase": true, "DropTargetTablesIfExists": true}