2025-06-18 22:19:52.272 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql-ledgerdata.config.json
2025-06-18 22:24:28.428 +08:00 [INF] 获取到8411个表
2025-06-18 22:24:28.472 +08:00 [INF] 发现分区表 LedgerData_LedgerAuditedData，包含 100 个分区
2025-06-18 22:24:28.475 +08:00 [INF] 合并分区表 LedgerData_LedgerAuditedData，总行数: 0，分区数据源: LedgerData_LedgerAuditedData_0(0), LedgerData_LedgerAuditedData_1(0), LedgerData_LedgerAuditedData_10(0), LedgerData_LedgerAuditedData_11(0), LedgerData_LedgerAuditedData_12(0), LedgerData_LedgerAuditedData_13(0), LedgerData_LedgerAuditedData_14(0), LedgerData_LedgerAuditedData_15(0), LedgerData_LedgerAuditedData_16(0), LedgerData_LedgerAuditedData_17(0), LedgerData_LedgerAuditedData_18(0), LedgerData_LedgerAuditedData_19(0), LedgerData_LedgerAuditedData_2(0), LedgerData_LedgerAuditedData_20(0), LedgerData_LedgerAuditedData_21(0), LedgerData_LedgerAuditedData_22(0), LedgerData_LedgerAuditedData_23(0), LedgerData_LedgerAuditedData_24(0), LedgerData_LedgerAuditedData_25(0), LedgerData_LedgerAuditedData_26(0), LedgerData_LedgerAuditedData_27(0), LedgerData_LedgerAuditedData_28(0), LedgerData_LedgerAuditedData_29(0), LedgerData_LedgerAuditedData_3(0), LedgerData_LedgerAuditedData_30(0), LedgerData_LedgerAuditedData_31(0), LedgerData_LedgerAuditedData_32(0), LedgerData_LedgerAuditedData_33(0), LedgerData_LedgerAuditedData_34(0), LedgerData_LedgerAuditedData_35(0), LedgerData_LedgerAuditedData_36(0), LedgerData_LedgerAuditedData_37(0), LedgerData_LedgerAuditedData_38(0), LedgerData_LedgerAuditedData_39(0), LedgerData_LedgerAuditedData_4(0), LedgerData_LedgerAuditedData_40(0), LedgerData_LedgerAuditedData_41(0), LedgerData_LedgerAuditedData_42(0), LedgerData_LedgerAuditedData_43(0), LedgerData_LedgerAuditedData_44(0), LedgerData_LedgerAuditedData_45(0), LedgerData_LedgerAuditedData_46(0), LedgerData_LedgerAuditedData_47(0), LedgerData_LedgerAuditedData_48(0), LedgerData_LedgerAuditedData_49(0), LedgerData_LedgerAuditedData_5(0), LedgerData_LedgerAuditedData_50(0), LedgerData_LedgerAuditedData_51(0), LedgerData_LedgerAuditedData_52(0), LedgerData_LedgerAuditedData_53(0), LedgerData_LedgerAuditedData_54(0), LedgerData_LedgerAuditedData_55(0), LedgerData_LedgerAuditedData_56(0), LedgerData_LedgerAuditedData_57(0), LedgerData_LedgerAuditedData_58(0), LedgerData_LedgerAuditedData_59(0), LedgerData_LedgerAuditedData_6(0), LedgerData_LedgerAuditedData_60(0), LedgerData_LedgerAuditedData_61(0), LedgerData_LedgerAuditedData_62(0), LedgerData_LedgerAuditedData_63(0), LedgerData_LedgerAuditedData_64(0), LedgerData_LedgerAuditedData_65(0), LedgerData_LedgerAuditedData_66(0), LedgerData_LedgerAuditedData_67(0), LedgerData_LedgerAuditedData_68(0), LedgerData_LedgerAuditedData_69(0), LedgerData_LedgerAuditedData_7(0), LedgerData_LedgerAuditedData_70(0), LedgerData_LedgerAuditedData_71(0), LedgerData_LedgerAuditedData_72(0), LedgerData_LedgerAuditedData_73(0), LedgerData_LedgerAuditedData_74(0), LedgerData_LedgerAuditedData_75(0), LedgerData_LedgerAuditedData_76(0), LedgerData_LedgerAuditedData_77(0), LedgerData_LedgerAuditedData_78(0), LedgerData_LedgerAuditedData_79(0), LedgerData_LedgerAuditedData_8(0), LedgerData_LedgerAuditedData_80(0), LedgerData_LedgerAuditedData_81(0), LedgerData_LedgerAuditedData_82(0), LedgerData_LedgerAuditedData_83(0), LedgerData_LedgerAuditedData_84(0), LedgerData_LedgerAuditedData_85(0), LedgerData_LedgerAuditedData_86(0), LedgerData_LedgerAuditedData_87(0), LedgerData_LedgerAuditedData_88(0), LedgerData_LedgerAuditedData_89(0), LedgerData_LedgerAuditedData_9(0), LedgerData_LedgerAuditedData_90(0), LedgerData_LedgerAuditedData_91(0), LedgerData_LedgerAuditedData_92(0), LedgerData_LedgerAuditedData_93(0), LedgerData_LedgerAuditedData_94(0), LedgerData_LedgerAuditedData_95(0), LedgerData_LedgerAuditedData_96(0), LedgerData_LedgerAuditedData_97(0), LedgerData_LedgerAuditedData_98(0), LedgerData_LedgerAuditedData_99(0)
2025-06-18 22:24:28.475 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 处理完成，将作为单个分区表迁移
2025-06-18 22:24:28.475 +08:00 [INF] 分区表处理完成，原始表数: 101，处理后表数: 1
2025-06-18 22:24:28.475 +08:00 [INF] 过滤后剩余1个表
2025-06-18 22:24:28.476 +08:00 [INF] 使用并行度: 4
2025-06-18 22:24:28.487 +08:00 [INF] 开始处理以下表的迁移: LedgerData_LedgerAuditedData
2025-06-18 22:24:28.487 +08:00 [INF] 表的迁移顺序: LedgerData_LedgerAuditedData
2025-06-18 22:24:29.677 +08:00 [INF] 表 LedgerData_LedgerAuditedData 是分区表，包含 100 个分区，使用分区表迁移逻辑
2025-06-18 22:24:29.681 +08:00 [INF] 开始迁移分区表 LedgerData_LedgerAuditedData，共 100 个分区
2025-06-18 22:24:29.683 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 包含 100 个数据源
2025-06-18 22:24:29.683 +08:00 [INF] 开始迁移分区数据源 LedgerData_LedgerAuditedData_0（表 LedgerData_LedgerAuditedData）
2025-06-18 22:24:29.688 +08:00 [ERR] 迁移分区表 LedgerData_LedgerAuditedData 时出错: Unable to cast object of type 'TryCode.DatabaseMigration.Parallel.CheckpointManagerAdapter' to type 'TryCode.DatabaseMigration.Checkpoint.CheckpointManager'.
2025-06-18 22:24:29.688 +08:00 [ERR] 迁移表 LedgerData_LedgerAuditedData 数据时出错: Unable to cast object of type 'TryCode.DatabaseMigration.Parallel.CheckpointManagerAdapter' to type 'TryCode.DatabaseMigration.Checkpoint.CheckpointManager'.
2025-06-18 22:24:29.714 +08:00 [INF] 正在生成迁移报告...
2025-06-18 22:24:29.718 +08:00 [INF] 迁移报告已生成: migration_report.txt
2025-06-18 22:24:29.719 +08:00 [ERR] 未知错误: Unable to cast object of type 'TryCode.DatabaseMigration.Parallel.CheckpointManagerAdapter' to type 'TryCode.DatabaseMigration.Checkpoint.CheckpointManager'.
System.InvalidCastException: Unable to cast object of type 'TryCode.DatabaseMigration.Parallel.CheckpointManagerAdapter' to type 'TryCode.DatabaseMigration.Checkpoint.CheckpointManager'.
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.MigratePartitionedTableAsync(TableSchema table, TableCheckpoint tableCheckpoint, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 977
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.MigratePartitionedTableAsync(TableSchema table, TableCheckpoint tableCheckpoint, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 1110
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 280
--- End of stack trace from previous location ---
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 451
--- End of stack trace from previous location ---
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 649
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 673
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 683
   at TryCode.DatabaseMigration.CLI.Commands.MigrationCommand.ExecuteMigrationCommandAsync() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.CLI/Commands/MigrationCommand.cs:line 199
2025-06-18 22:24:29.735 +08:00 [INF] 正在关闭日志记录器...
