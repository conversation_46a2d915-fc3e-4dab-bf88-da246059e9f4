using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Configuration.Models;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Parallel;

namespace TryCode.DatabaseMigration.CLI
{
    /// <summary>
    /// 分区表迁移演示程序
    /// </summary>
    public class PartitionTableMigrationDemo
    {
        private readonly ILogger<PartitionTableMigrationDemo> _logger;

        public PartitionTableMigrationDemo(ILogger<PartitionTableMigrationDemo> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 演示分区表处理逻辑
        /// </summary>
        public void DemonstratePartitionTableProcessing()
        {
            _logger.LogInformation("=== 分区表迁移逻辑演示 ===");

            // 模拟从PostgreSQL获取的表列表（包括主分区表和各个分区）
            var allTables = CreateMockTableList();

            _logger.LogInformation("原始表列表（共 {Count} 个表）:", allTables.Count);
            foreach (var table in allTables)
            {
                _logger.LogInformation("- {TableName} (IsPartitioned: {IsPartitioned}, Partitions: {PartitionCount})",
                    table.Name, table.IsPartitioned, table.Partitions.Count);
            }

            // 模拟配置（包含所有表名，这是推荐的配置方式）
            var config = new MigrationConfig
            {
                Tables = allTables.Select(t => t.Name).ToList()
            };

            // 创建模拟的并行迁移执行器来测试ProcessPartitionTables方法
            var executor = new MockParallelMigrationExecutor(config, _logger);
            var processedTables = executor.TestProcessPartitionTables(allTables);

            _logger.LogInformation("\n处理后的表列表（共 {Count} 个表）:", processedTables.Count);
            foreach (var table in processedTables)
            {
                _logger.LogInformation("- {TableName} (IsPartitioned: {IsPartitioned}, EstimatedRows: {RowCount})",
                    table.Name, table.IsPartitioned, table.EstimatedRowCount);

                if (table.AdditionalInfo.TryGetValue("PartitionDataSources", out var sources))
                {
                    _logger.LogInformation("  分区数据源: {Sources}",
                        string.Join(", ", ((List<Dictionary<string, object>>)sources)
                            .Select(s => $"{s["Name"]}({s["RowCount"]})")));
                }
            }

            _logger.LogInformation("\n=== 演示完成 ===");
            _logger.LogInformation("总结：原始8个表（1主表+5分区+2普通表）被正确处理为3个表（1分区表+2普通表）");
            _logger.LogInformation("这避免了在目标数据库中创建多个独立的分区表，而是创建一个正确的分区表结构。");
        }

        /// <summary>
        /// 创建模拟的表列表，模拟PostgreSQL中的分区表结构
        /// </summary>
        private List<TableSchema> CreateMockTableList()
        {
            var tables = new List<TableSchema>();

            // 1. 主分区表 LedgerData_LedgerAuditedData
            var mainTable = new TableSchema
            {
                Name = "LedgerData_LedgerAuditedData",
                IsPartitioned = true,
                PartitionType = "HASH",
                PartitionKey = "id",
                EstimatedRowCount = 0, // 主表本身没有数据
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema { Name = "id", DataType = "bigint", IsPrimaryKey = true },
                    new ColumnSchema { Name = "data", DataType = "text" },
                    new ColumnSchema { Name = "created_at", DataType = "timestamp" }
                },
                PrimaryKeys = new List<string> { "id" },
                Partitions = new List<PartitionDefinition>()
            };

            // 2. 添加分区定义到主表
            for (int i = 0; i < 5; i++) // 只创建5个分区用于演示
            {
                mainTable.Partitions.Add(new PartitionDefinition
                {
                    Name = $"LedgerData_LedgerAuditedData_{i}",
                    Condition = $"FOR VALUES WITH (MODULUS 100, REMAINDER {i})"
                });
            }

            tables.Add(mainTable);

            // 3. 添加各个分区表（在PostgreSQL中这些会被当作独立的表查询出来）
            for (int i = 0; i < 5; i++)
            {
                var partitionTable = new TableSchema
                {
                    Name = $"LedgerData_LedgerAuditedData_{i}",
                    IsPartitioned = false, // 分区本身不是分区表
                    EstimatedRowCount = 1000 + i * 500, // 模拟不同的行数
                    Columns = mainTable.Columns, // 与主表相同的列结构
                    PrimaryKeys = mainTable.PrimaryKeys
                };
                tables.Add(partitionTable);
            }

            // 4. 添加一些普通表
            tables.Add(new TableSchema
            {
                Name = "RegularTable1",
                IsPartitioned = false,
                EstimatedRowCount = 2000,
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema { Name = "id", DataType = "int", IsPrimaryKey = true },
                    new ColumnSchema { Name = "name", DataType = "varchar" }
                },
                PrimaryKeys = new List<string> { "id" }
            });

            tables.Add(new TableSchema
            {
                Name = "RegularTable2",
                IsPartitioned = false,
                EstimatedRowCount = 1500,
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema { Name = "id", DataType = "int", IsPrimaryKey = true },
                    new ColumnSchema { Name = "description", DataType = "text" }
                },
                PrimaryKeys = new List<string> { "id" }
            });

            return tables;
        }
    }

    /// <summary>
    /// 模拟的并行迁移执行器，用于测试分区表处理逻辑
    /// </summary>
    public class MockParallelMigrationExecutor
    {
        private readonly MigrationConfig _config;
        private readonly ILogger _logger;

        public MockParallelMigrationExecutor(MigrationConfig config, ILogger logger)
        {
            _config = config;
            _logger = logger;
        }



        /// <summary>
        /// 测试分区表处理方法（从ParallelMigrationExecutor复制的逻辑）
        /// </summary>
        public List<TableSchema> TestProcessPartitionTables(List<TableSchema> tables)
        {
            var result = new List<TableSchema>();
            var processedTables = new HashSet<string>();

            // 按表名排序，确保主表在分区之前处理
            var sortedTables = tables.OrderBy(t => t.Name).ToList();

            foreach (var table in sortedTables)
            {
                if (processedTables.Contains(table.Name))
                    continue;

                // 检查是否是分区表的主表
                if (table.IsPartitioned && table.Partitions.Count > 0)
                {
                    _logger.LogInformation("发现分区表 {TableName}，包含 {PartitionCount} 个分区", 
                        table.Name, table.Partitions.Count);

                    // 收集所有相关的分区表数据
                    var allPartitionData = new List<TableSchema>();
                    allPartitionData.Add(table); // 主表

                    // 查找所有分区表
                    foreach (var partition in table.Partitions)
                    {
                        var partitionTable = tables.FirstOrDefault(t => t.Name == partition.Name);
                        if (partitionTable != null)
                        {
                            allPartitionData.Add(partitionTable);
                            processedTables.Add(partitionTable.Name);
                            _logger.LogDebug("找到分区表 {PartitionName}，将合并到主表 {MainTable}", 
                                partition.Name, table.Name);
                        }
                    }

                    // 创建合并后的分区表结构
                    var mergedTable = CreateMergedPartitionTable(table, allPartitionData);
                    result.Add(mergedTable);
                    processedTables.Add(table.Name);

                    _logger.LogInformation("分区表 {TableName} 处理完成，将作为单个分区表迁移", table.Name);
                }
                else
                {
                    // 检查是否是某个分区表的分区
                    bool isPartitionOfAnotherTable = sortedTables.Any(t => 
                        t.IsPartitioned && 
                        t.Partitions.Any(p => p.Name == table.Name));

                    if (!isPartitionOfAnotherTable)
                    {
                        // 普通表，直接添加
                        result.Add(table);
                        processedTables.Add(table.Name);
                    }
                    else
                    {
                        _logger.LogDebug("表 {TableName} 是分区表的分区，将被合并到主表中", table.Name);
                        processedTables.Add(table.Name);
                    }
                }
            }

            _logger.LogInformation("分区表处理完成，原始表数: {OriginalCount}，处理后表数: {ProcessedCount}", 
                tables.Count, result.Count);

            return result;
        }

        private TableSchema CreateMergedPartitionTable(TableSchema mainTable, List<TableSchema> allPartitionData)
        {
            var mergedTable = new TableSchema
            {
                Name = mainTable.Name,
                Columns = mainTable.Columns,
                ForeignKeys = mainTable.ForeignKeys,
                PrimaryKeys = mainTable.PrimaryKeys,
                IsPartitioned = true,
                PartitionKey = mainTable.PartitionKey,
                PartitionKeys = mainTable.PartitionKeys,
                PartitionType = mainTable.PartitionType,
                PartitionCount = mainTable.PartitionCount,
                Partitions = mainTable.Partitions,
                AdditionalInfo = mainTable.AdditionalInfo
            };

            // 计算总行数（所有分区的行数之和）
            mergedTable.EstimatedRowCount = allPartitionData.Sum(t => t.EstimatedRowCount);

            // 添加分区数据信息到AdditionalInfo中，供后续迁移使用
            var partitionDataInfo = allPartitionData.Where(t => t.Name != mainTable.Name)
                .Select(t => new Dictionary<string, object>
                {
                    ["Name"] = t.Name,
                    ["RowCount"] = t.EstimatedRowCount
                })
                .ToList();

            mergedTable.AdditionalInfo["PartitionDataSources"] = partitionDataInfo;

            _logger.LogInformation("合并分区表 {TableName}，总行数: {TotalRows}，分区数据源: {PartitionSources}", 
                mergedTable.Name, mergedTable.EstimatedRowCount, 
                string.Join(", ", partitionDataInfo.Select(p => $"{p["Name"]}({p["RowCount"]})")));

            return mergedTable;
        }
    }
}
